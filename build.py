#!/usr/bin/env python3
"""
Windows PyInstaller Build Script for Auto Shutdown Application
Builds the application and packages it with static files into a zip archive
"""

import os
import sys
import shutil
import subprocess
import zipfile
import time
from pathlib import Path
import tempfile


class BuildScript:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.static_dir = self.project_root / "static"
        self.app_name = "AutoShutdown"
        self.version = "1.0.0"
        
    def clean_previous_builds(self):
        """Clean previous build artifacts"""
        print("🧹 Cleaning previous builds...")

        # Try to remove dist directory with retry logic for Windows file locking
        if self.dist_dir.exists():
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    shutil.rmtree(self.dist_dir)
                    print(f"   Removed {self.dist_dir}")
                    break
                except PermissionError as e:
                    if attempt < max_retries - 1:
                        print(f"   ⚠️  Attempt {attempt + 1} failed, retrying in 2 seconds...")
                        time.sleep(2)
                    else:
                        print(f"   ⚠️  Could not remove {self.dist_dir}, some files may be locked")
                        print(f"   You may need to manually delete the dist folder or restart your system")
                        # Continue anyway, PyInstaller will overwrite what it can

        if self.build_dir.exists():
            try:
                shutil.rmtree(self.build_dir)
                print(f"   Removed {self.build_dir}")
            except PermissionError:
                print(f"   ⚠️  Could not remove {self.build_dir}, continuing anyway...")

        # Remove any existing spec files
        for spec_file in self.project_root.glob("*.spec"):
            try:
                spec_file.unlink()
                print(f"   Removed {spec_file}")
            except PermissionError:
                print(f"   ⚠️  Could not remove {spec_file}, continuing anyway...")
    
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        try:
            import PyInstaller
            print(f"   ✅ PyInstaller {PyInstaller.__version__} found")
        except ImportError:
            print("   ❌ PyInstaller not found. Installing...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("   ✅ PyInstaller installed")
        
        # Check if requirements.txt dependencies are installed
        if (self.project_root / "requirements.txt").exists():
            print("   📦 Installing project dependencies...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", 
                str(self.project_root / "requirements.txt")
            ], check=True)
            print("   ✅ Project dependencies installed")
    
    def create_pyinstaller_spec(self):
        """Create PyInstaller spec file"""
        print("📝 Creating PyInstaller spec file...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=['{self.project_root.as_posix()}'],
    binaries=[],
    datas=[
        ('static', 'static'),
        ('config.json', '.'),
    ],
    hiddenimports=[
        'PIL._tkinter_finder',
        'pystray._win32',
        'webview.platforms.winforms',
        'webview.platforms.cef',
        'webview.platforms.mshtml',
        'schedule',
        'apscheduler.schedulers.blocking',
        'apscheduler.schedulers.background',
        'apscheduler.executors.pool',
        'apscheduler.jobstores.memory',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Set to False for windowed app
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon path here if you have one
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{self.app_name}',
)
'''
        
        spec_file = self.project_root / f"{self.app_name}.spec"
        spec_file.write_text(spec_content)
        print(f"   ✅ Created {spec_file}")
        return spec_file
    
    def build_executable(self, spec_file):
        """Build the executable using PyInstaller"""
        print("🔨 Building executable with PyInstaller...")
        
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            str(spec_file)
        ]
        
        print(f"   Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"   ❌ Build failed!")
            print(f"   STDOUT: {result.stdout}")
            print(f"   STDERR: {result.stderr}")
            return False
        
        print("   ✅ Build completed successfully")
        return True
    
    def verify_build(self):
        """Verify that the build was successful"""
        print("🔍 Verifying build...")

        app_dir = self.dist_dir / self.app_name
        exe_file = app_dir / f"{self.app_name}.exe"

        # In newer PyInstaller versions, data files are in _internal directory
        static_dir = app_dir / "_internal" / "static"
        config_file = app_dir / "_internal" / "config.json"

        if not exe_file.exists():
            print(f"   ❌ Executable not found: {exe_file}")
            return False

        if not static_dir.exists():
            print(f"   ❌ Static directory not found: {static_dir}")
            return False

        if not config_file.exists():
            print(f"   ❌ Config file not found: {config_file}")
            return False

        # Check static files
        expected_files = ["settings.html", "shutdown_overlay.html", "shutdown_overlay.js", "sentences.json", "LXGWWenKai-Regular.ttf"]
        for file_name in expected_files:
            file_path = static_dir / file_name
            if not file_path.exists():
                print(f"   ❌ Static file missing: {file_path}")
                return False

        print("   ✅ Build verification passed")
        print(f"   📁 Executable: {exe_file}")
        print(f"   📁 Static files: {static_dir}")
        print(f"   📁 Config file: {config_file}")
        return True
    
    def create_zip_package(self):
        """Create a zip package of the built application"""
        print("📦 Creating zip package...")
        
        app_dir = self.dist_dir / self.app_name
        zip_name = f"{self.app_name}-v{self.version}-windows.zip"
        zip_path = self.dist_dir / zip_name
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(app_dir):
                for file in files:
                    file_path = Path(root) / file
                    # Calculate relative path from app_dir
                    arcname = file_path.relative_to(app_dir)
                    zipf.write(file_path, arcname)
                    
        print(f"   ✅ Created {zip_path}")
        print(f"   📊 Package size: {zip_path.stat().st_size / 1024 / 1024:.1f} MB")
        return zip_path
    
    def create_readme(self):
        """Create a README file for the distribution"""
        print("📄 Creating distribution README...")

        readme_content = f"""# {self.app_name} v{self.version}

## Installation
1. Extract the zip file to your desired location
2. Run `{self.app_name}.exe` to start the application

## Features
- System tray integration
- Scheduled shutdown functionality
- Beautiful slide-to-cancel interface
- Configuration management

## Files Structure
- `{self.app_name}.exe` - Main application executable
- `_internal/` - Application data and dependencies
  - `static/` - Static assets (HTML, CSS, JS, fonts)
  - `config.json` - Default configuration file
  - [other runtime files] - Required libraries and dependencies

## Usage
1. Double-click `{self.app_name}.exe` to start
2. The application will appear in your system tray
3. Right-click the tray icon to access settings and scheduling options
4. Configuration is automatically saved and loaded

## System Requirements
- Windows 10 or later
- No additional dependencies required
- Approximately 50MB disk space

## Support
For issues and support, please refer to the project documentation.

Built with PyInstaller on {Path.cwd()}
Build Date: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""

        readme_path = self.dist_dir / self.app_name / "README.txt"
        readme_path.write_text(readme_content, encoding='utf-8')
        print(f"   ✅ Created {readme_path}")
    
    def build(self):
        """Main build process"""
        print(f"🚀 Starting build process for {self.app_name} v{self.version}")
        print("=" * 60)
        
        try:
            # Step 1: Clean previous builds
            self.clean_previous_builds()
            
            # Step 2: Check dependencies
            self.check_dependencies()
            
            # Step 3: Create spec file
            spec_file = self.create_pyinstaller_spec()
            
            # Step 4: Build executable
            if not self.build_executable(spec_file):
                return False
            
            # Step 5: Verify build
            if not self.verify_build():
                return False
            
            # Step 6: Create README
            self.create_readme()
            
            # Step 7: Create zip package
            zip_path = self.create_zip_package()
            
            print("=" * 60)
            print("🎉 Build completed successfully!")
            print(f"📦 Package: {zip_path}")
            print(f"📁 Build directory: {self.dist_dir / self.app_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ Build failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    builder = BuildScript()
    success = builder.build()
    sys.exit(0 if success else 1)
