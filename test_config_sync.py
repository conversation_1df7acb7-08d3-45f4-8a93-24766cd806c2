#!/usr/bin/env python3
"""
Test script to verify config synchronization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_manager import Config<PERSON>ana<PERSON>

def test_config_loading():
    """Test config loading functionality"""
    print("Testing config loading...")
    
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    print("Loaded config:")
    print(f"  Schedules: {len(config.get('schedules', []))}")
    print(f"  Settings: {config.get('settings', {})}")
    
    # Print detailed schedule info
    for i, schedule in enumerate(config.get('schedules', [])):
        print(f"  Schedule {i+1}:")
        print(f"    Name: {schedule.get('name', 'N/A')}")
        print(f"    Time: {schedule.get('time', 'N/A')}")
        print(f"    Days: {schedule.get('days', [])}")
        print(f"    Enabled: {schedule.get('enabled', False)}")
        print(f"    ID: {schedule.get('id', 'N/A')}")
    
    return config

if __name__ == "__main__":
    test_config_loading()
