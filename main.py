"""
Auto Shutdown Application
A beautiful system tray application for scheduled shutdowns with slide-to-cancel interface
"""

import threading
import time
from pathlib import Path

from config_manager import Config<PERSON>anager
from tray_manager import Tray<PERSON>anager
from scheduler_manager import SchedulerManager
from window_manager import WindowManager


class AutoShutdownApp:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.is_running = True
        self.reload_signal_file = Path("scheduler_reload.signal")
        self.last_reload_time = 0

        # Initialize managers
        self.window_manager = WindowManager()
        self.scheduler_manager = SchedulerManager(self.config_manager, self.window_manager)
        self.tray_manager = TrayManager(self.config_manager, self.window_manager, self.scheduler_manager)

        # Note: SchedulerManager now automatically loads schedules in __init__

    def run(self):
        """Run the application"""
        # Create and run tray icon in separate thread
        tray_thread = threading.Thread(target=self.run_tray, daemon=True)
        tray_thread.start()

        # Start config monitor thread
        monitor_thread = threading.Thread(target=self.monitor_config_changes, daemon=True)
        monitor_thread.start()

        # Keep main thread alive
        try:
            while self.is_running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.quit_app()

    def run_tray(self):
        """Run system tray"""
        icon = self.tray_manager.create_tray_icon()
        icon.run()

    def monitor_config_changes(self):
        """Monitor for configuration changes and reload schedules"""
        while self.is_running:
            try:
                if self.reload_signal_file.exists():
                    # Read the timestamp from the signal file
                    signal_time = float(self.reload_signal_file.read_text().strip())

                    # Only reload if this is a new signal
                    if signal_time > self.last_reload_time:
                        print("Configuration change detected, reloading schedules...")
                        self.scheduler_manager.reload_schedules()
                        self.last_reload_time = signal_time

                        # Remove the signal file
                        self.reload_signal_file.unlink()

            except Exception as e:
                print(f"Error monitoring config changes: {e}")

            time.sleep(1)  # Check every second

    def quit_app(self):
        """Quit the application"""
        self.is_running = False

        # Clean up signal file
        try:
            if self.reload_signal_file.exists():
                self.reload_signal_file.unlink()
        except Exception as e:
            print(f"Error cleaning up signal file: {e}")

        self.tray_manager.quit_app()


if __name__ == "__main__":
    app = AutoShutdownApp()
    app.run()
