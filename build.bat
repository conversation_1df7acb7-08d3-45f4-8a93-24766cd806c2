@echo off
echo ========================================
echo Auto Shutdown Application Build Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python found, starting build process...
echo.

REM Run the build script
python build.py

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above for details.
    pause
    exit /b 1
) else (
    echo.
    echo BUILD SUCCESSFUL!
    echo Check the dist folder for the generated package.
    echo.
    pause
)
